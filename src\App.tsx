import React, { useState } from 'react';
import Sidebar from './components/Sidebar';
import KanbanBoard from './components/KanbanBoard';
import { Search, UserPlus } from 'lucide-react';
import { useStore } from './store/useStore';
import UserManager from './components/UserManager';

function App() {
  const { tasks, moveTask } = useStore();
  const [showUserManager, setShowUserManager] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold">Project Dashboard</h1>
              <p className="text-gray-600">Track and manage your tasks efficiently</p>
            </div>
            
            <div className="flex items-center gap-4">
              

              <button
                onClick={() => setShowUserManager(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <UserPlus className="w-5 h-5" />
                Manage Users
              </button>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
            <div className="grid grid-cols-4 gap-4">
              {[
                { label: 'Total Tasks', value: tasks.length },
                { label: 'In Progress', value: tasks.filter(t => t.status === 'in-progress').length },
                { label: 'Completed', value: tasks.filter(t => t.status === 'done').length },
                { label: 'Due Soon', value: tasks.filter(t => t.dueDate && new Date(t.dueDate) <= new Date()).length }
              ].map(({ label, value }) => (
                <div key={label} className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{label}</p>
                  <p className="text-2xl font-bold mt-1">{value}</p>
                </div>
              ))}
            </div>
          </div>

          <KanbanBoard tasks={tasks} onTaskMove={moveTask} />
        </div>
      </main>

      {showUserManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-white">User Management</h2>
                <button
                  onClick={() => setShowUserManager(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>
              <UserManager />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;