import React, { useState, useEffect } from 'react';
import { useStore } from '../store/useStore';
import { Clock, User, Plus, Trash2 } from 'lucide-react';
import { TaskEffort } from '../types';

interface TaskEffortEstimatorProps {
  taskId?: string;
  initialEffort?: TaskEffort;
  onEffortChange: (effort: TaskEffort) => void;
  disabled?: boolean;
}

export default function TaskEffortEstimator({ 
  taskId, 
  initialEffort, 
  onEffortChange, 
  disabled = false 
}: TaskEffortEstimatorProps) {
  const { skillsetGroups, users } = useStore();
  
  const [effort, setEffort] = useState<TaskEffort>({
    taskId: taskId || '',
    skillsetEstimates: initialEffort?.skillsetEstimates || [],
    totalEstimatedHours: initialEffort?.totalEstimatedHours || 0,
    totalActualHours: initialEffort?.totalActualHours || 0,
  });

  // Update effort when props change
  useEffect(() => {
    if (initialEffort) {
      setEffort(initialEffort);
    }
  }, [initialEffort]);

  // Calculate total estimated hours whenever skillset estimates change
  useEffect(() => {
    const total = effort.skillsetEstimates.reduce((sum, estimate) => sum + estimate.estimatedHours, 0);
    const updatedEffort = { ...effort, totalEstimatedHours: total };
    setEffort(updatedEffort);
    onEffortChange(updatedEffort);
  }, [effort.skillsetEstimates]);

  const addSkillsetEstimate = () => {
    const availableSkillsets = skillsetGroups.filter(
      skillset => !effort.skillsetEstimates.some(est => est.skillsetId === skillset.id)
    );
    
    if (availableSkillsets.length > 0) {
      const newEstimate = {
        skillsetId: availableSkillsets[0].id,
        estimatedHours: 0,
        actualHours: 0,
        assignedUserId: undefined,
      };
      
      setEffort({
        ...effort,
        skillsetEstimates: [...effort.skillsetEstimates, newEstimate]
      });
    }
  };

  const updateSkillsetEstimate = (index: number, field: keyof typeof effort.skillsetEstimates[0], value: any) => {
    const updated = [...effort.skillsetEstimates];
    updated[index] = { ...updated[index], [field]: value };
    setEffort({ ...effort, skillsetEstimates: updated });
  };

  const removeSkillsetEstimate = (index: number) => {
    const updated = effort.skillsetEstimates.filter((_, i) => i !== index);
    setEffort({ ...effort, skillsetEstimates: updated });
  };

  const getAvailableUsers = (skillsetId: string) => {
    return users.filter(user => user.skillsetIds?.includes(skillsetId));
  };

  const getSkillsetName = (skillsetId: string) => {
    const skillset = skillsetGroups.find(s => s.id === skillsetId);
    return skillset ? skillset.name : 'Unknown Skillset';
  };

  const getSkillsetColor = (skillsetId: string) => {
    const skillset = skillsetGroups.find(s => s.id === skillsetId);
    return skillset ? skillset.color : 'bg-gray-500';
  };

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unassigned';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Effort Estimation
        </h4>
        {!disabled && (
          <button
            type="button"
            onClick={addSkillsetEstimate}
            disabled={effort.skillsetEstimates.length >= skillsetGroups.length}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            Add Skillset
          </button>
        )}
      </div>

      {effort.skillsetEstimates.length === 0 ? (
        <div className="text-center py-6 text-gray-500">
          <Clock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No effort estimates defined</p>
          {!disabled && (
            <button
              type="button"
              onClick={addSkillsetEstimate}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm"
            >
              Add your first estimate
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {effort.skillsetEstimates.map((estimate, index) => (
            <div key={index} className="bg-gray-50 p-3 rounded-lg border">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${getSkillsetColor(estimate.skillsetId)}`} />
                  <span className="font-medium">{getSkillsetName(estimate.skillsetId)}</span>
                </div>
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeSkillsetEstimate(index)}
                    className="text-red-500 hover:text-red-700 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Skillset
                  </label>
                  <select
                    value={estimate.skillsetId}
                    onChange={(e) => updateSkillsetEstimate(index, 'skillsetId', e.target.value)}
                    disabled={disabled}
                    className="w-full border rounded p-2 text-sm"
                  >
                    {skillsetGroups.map((skillset) => (
                      <option key={skillset.id} value={skillset.id}>
                        {skillset.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Estimated Hours
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.5"
                    value={estimate.estimatedHours}
                    onChange={(e) => updateSkillsetEstimate(index, 'estimatedHours', parseFloat(e.target.value) || 0)}
                    disabled={disabled}
                    className="w-full border rounded p-2 text-sm"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Assigned User
                  </label>
                  <select
                    value={estimate.assignedUserId || ''}
                    onChange={(e) => updateSkillsetEstimate(index, 'assignedUserId', e.target.value || undefined)}
                    disabled={disabled}
                    className="w-full border rounded p-2 text-sm"
                  >
                    <option value="">Unassigned</option>
                    {getAvailableUsers(estimate.skillsetId).map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {estimate.actualHours !== undefined && (
                <div className="mt-3 pt-3 border-t">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        Actual Hours
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.5"
                        value={estimate.actualHours}
                        onChange={(e) => updateSkillsetEstimate(index, 'actualHours', parseFloat(e.target.value) || 0)}
                        disabled={disabled}
                        className="w-full border rounded p-2 text-sm"
                        placeholder="0"
                      />
                    </div>
                    <div className="flex items-end">
                      <div className="text-xs text-gray-600">
                        <div>Variance: {((estimate.actualHours || 0) - estimate.estimatedHours).toFixed(1)}h</div>
                        <div className={`${
                          (estimate.actualHours || 0) > estimate.estimatedHours 
                            ? 'text-red-600' 
                            : 'text-green-600'
                        }`}>
                          {estimate.estimatedHours > 0 
                            ? `${(((estimate.actualHours || 0) / estimate.estimatedHours - 1) * 100).toFixed(0)}%`
                            : 'N/A'
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {effort.skillsetEstimates.length > 0 && (
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="flex justify-between items-center text-sm">
            <span className="font-medium">Total Estimated Hours:</span>
            <span className="font-bold text-blue-700">{effort.totalEstimatedHours.toFixed(1)}h</span>
          </div>
          {effort.totalActualHours !== undefined && effort.totalActualHours > 0 && (
            <div className="flex justify-between items-center text-sm mt-1">
              <span className="font-medium">Total Actual Hours:</span>
              <span className="font-bold text-blue-700">{effort.totalActualHours.toFixed(1)}h</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
