import React, { useState, useEffect } from 'react';
import { useStore } from '../store/useStore';
import { Clock, User } from 'lucide-react';
import { TaskEffort } from '../types';

interface TaskEffortEstimatorProps {
  taskId?: string;
  initialEffort?: TaskEffort;
  onEffortChange: (effort: TaskEffort) => void;
  disabled?: boolean;
}

export default function TaskEffortEstimator({ 
  taskId, 
  initialEffort, 
  onEffortChange, 
  disabled = false 
}: TaskEffortEstimatorProps) {
  const { users } = useStore();
  
  const [effort, setEffort] = useState<TaskEffort>({
    taskId: taskId || '',
    estimatedHours: initialEffort?.estimatedHours || 0,
    actualHours: initialEffort?.actualHours || 0,
    assignedUserId: initialEffort?.assignedUserId || undefined,
  });

  // Update effort when props change
  useEffect(() => {
    if (initialEffort) {
      setEffort(initialEffort);
    }
  }, [initialEffort]);

  // Update effort when form changes
  const updateEffort = (field: keyof TaskEffort, value: any) => {
    const updatedEffort = { ...effort, [field]: value };
    setEffort(updatedEffort);
    onEffortChange(updatedEffort);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4" />
        <h4 className="font-medium">Effort Estimation</h4>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Estimated Hours */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Hours
            </label>
            <input
              type="number"
              min="0"
              step="0.5"
              value={effort.estimatedHours}
              onChange={(e) => updateEffort('estimatedHours', parseFloat(e.target.value) || 0)}
              disabled={disabled}
              className="w-full border rounded-lg p-2 disabled:bg-gray-100"
              placeholder="0"
            />
          </div>

          {/* Actual Hours */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Actual Hours
            </label>
            <input
              type="number"
              min="0"
              step="0.5"
              value={effort.actualHours || 0}
              onChange={(e) => updateEffort('actualHours', parseFloat(e.target.value) || 0)}
              disabled={disabled}
              className="w-full border rounded-lg p-2 disabled:bg-gray-100"
              placeholder="0"
            />
          </div>

          {/* Assigned User */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
              <User className="w-4 h-4" />
              Assigned User
            </label>
            <select
              value={effort.assignedUserId || ''}
              onChange={(e) => updateEffort('assignedUserId', e.target.value || undefined)}
              disabled={disabled}
              className="w-full border rounded-lg p-2 disabled:bg-gray-100"
            >
              <option value="">Unassigned</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Summary */}
        {(effort.estimatedHours > 0 || effort.actualHours > 0) && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated:</span>
                <span className="font-medium">{effort.estimatedHours.toFixed(1)}h</span>
              </div>
              {effort.actualHours > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Actual:</span>
                  <span className="font-medium">{effort.actualHours.toFixed(1)}h</span>
                </div>
              )}
              {effort.estimatedHours > 0 && effort.actualHours > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Variance:</span>
                  <span className={`font-medium ${effort.actualHours > effort.estimatedHours ? 'text-red-600' : 'text-green-600'}`}>
                    {effort.actualHours > effort.estimatedHours ? '+' : ''}
                    {(effort.actualHours - effort.estimatedHours).toFixed(1)}h
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
