import React, { useState } from 'react';
import { Layout, Home, CheckSquare, Calendar, Settings, Users, FolderKanban, Columns, LayoutGrid, List, Clock, Palette, UserCheck, BarChart3 } from 'lucide-react';
import UserGroupManager from './UserGroupManager';
import Timeline from './Timeline';
import ProjectManager from './ProjectManager';
import ColumnManager from './ColumnManager';
import SettingsPanel from './Settings';
import KanbanBoard from './KanbanBoard';
import TaskTreeSidebar from './TaskTreeSidebar';
import TaskListView from './TaskListView';
import SkillsetGroupManager from './SkillsetGroupManager';
import UserManager from './UserManager';
import ResourceCalendar from './ResourceCalendar';
import Analytics from './Analytics';
import { useStore } from '../store/useStore';

export default function Sidebar() {
  const { tasks, moveTask, tasksViewMode, setTasksViewMode } = useStore();
  const [activeSection, setActiveSection] = useState('dashboard');

  const menuItems = [
    { icon: Home, label: 'Dashboard', id: 'dashboard' },
    { icon: CheckSquare, label: 'Tasks', id: 'tasks' },
    { icon: FolderKanban, label: 'Projects', id: 'projects' },
    { icon: Calendar, label: 'Timeline', id: 'timeline' },
    { icon: Clock, label: 'Resource Calendar', id: 'resources' },
    { icon: BarChart3, label: 'Analytics', id: 'analytics' },
    { icon: UserCheck, label: 'Users', id: 'users' },
    { icon: Palette, label: 'Skillsets', id: 'skillsets' },
    { icon: Users, label: 'User Groups', id: 'groups' },
    { icon: Columns, label: 'Columns', id: 'columns' },
    { icon: Settings, label: 'Settings', id: 'settings' },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="p-8">
            <div className="max-w-7xl mx-auto">
              <Timeline />
            </div>
          </div>
        );
      case 'groups':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserGroupManager />
          </div>
        );
      case 'timeline':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Timeline />
          </div>
        );
      case 'projects':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ProjectManager />
          </div>
        );
      case 'columns':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ColumnManager />
          </div>
        );
      case 'settings':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SettingsPanel />
          </div>
        );
      case 'resources':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ResourceCalendar />
          </div>
        );
      case 'analytics':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Analytics />
          </div>
        );
      case 'users':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserManager />
          </div>
        );
      case 'skillsets':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SkillsetGroupManager />
          </div>
        );
      case 'tasks':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-hidden">
            <div className="h-full flex flex-col">
              {/* Header with view toggle */}
              <div className="p-6 bg-white border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h1 className="text-2xl font-bold">Tasks</h1>
                  <div className="flex bg-gray-100 rounded-lg p-1">
                    <button
                      onClick={() => setTasksViewMode('kanban')}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        tasksViewMode === 'kanban'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      <LayoutGrid className="w-4 h-4 inline mr-2" />
                      Kanban
                    </button>
                    <button
                      onClick={() => setTasksViewMode('list')}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        tasksViewMode === 'list'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      <List className="w-4 h-4 inline mr-2" />
                      List
                    </button>
                  </div>
                </div>
              </div>

              {/* Content area */}
              <div className="flex-1 overflow-hidden">
                {tasksViewMode === 'kanban' ? (
                  <div className="h-full overflow-auto p-8">
                    <KanbanBoard tasks={tasks} onTaskMove={moveTask} />
                  </div>
                ) : (
                  <div className="h-full flex">
                    <TaskTreeSidebar />
                    <TaskListView />
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="w-64 bg-gray-900 h-screen fixed left-0 top-0 text-white p-4 overflow-y-auto z-10">
        <div className="flex items-center gap-2 mb-8">
          <Layout className="w-8 h-8 text-blue-400" />
          <h1 className="text-xl font-bold">TaskFlow</h1>
        </div>
        
        <nav className="space-y-2">
          {menuItems.map(({ icon: Icon, label, id }) => (
            <button
              key={id}
              onClick={() => setActiveSection(id)}
              className={`flex items-center gap-3 w-full p-3 rounded-lg transition-colors ${
                activeSection === id ? 'bg-gray-800' : 'hover:bg-gray-800'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {renderContent()}
    </>
  );
}










