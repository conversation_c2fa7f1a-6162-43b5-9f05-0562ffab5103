import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { Plus, Trash2, Edit2 } from 'lucide-react';

export default function UserGroupManager() {
  const { userGroups, addUserGroup, updateUserGroup, deleteUserGroup } = useStore();
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [newGroupName, setNewGroupName] = useState('');

  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-indigo-500',
  ];

  const handleAddGroup = () => {
    if (newGroupName.trim()) {
      addUserGroup({
        name: newGroupName,
        color: colors[Math.floor(Math.random() * colors.length)],
      });
      setNewGroupName('');
    }
  };

  const handleUpdateGroup = (id: string, name: string) => {
    updateUserGroup(id, { name });
    setIsEditing(null);
  };

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold mb-4">User Groups</h3>
      
      <div className="space-y-2">
        {userGroups.map((group) => (
          <div
            key={group.id}
            className="flex items-center justify-between p-2 hover:bg-gray-800 rounded-lg"
          >
            {isEditing === group.id ? (
              <input
                type="text"
                value={group.name}
                onChange={(e) => handleUpdateGroup(group.id, e.target.value)}
                onBlur={() => setIsEditing(null)}
                autoFocus
                className="bg-gray-700 text-white px-2 py-1 rounded"
              />
            ) : (
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${group.color}`} />
                <span className="text-sm">{group.name}</span>
              </div>
            )}
            
            <div className="flex gap-2">
              <button
                onClick={() => setIsEditing(group.id)}
                className="p-1 hover:bg-gray-700 rounded"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={() => deleteUserGroup(group.id)}
                className="p-1 hover:bg-gray-700 rounded text-red-400"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 flex gap-2">
        <input
          type="text"
          value={newGroupName}
          onChange={(e) => setNewGroupName(e.target.value)}
          placeholder="New group name"
          className="bg-gray-700 text-white px-3 py-1 rounded flex-1"
        />
        <button
          onClick={handleAddGroup}
          className="p-2 hover:bg-gray-700 rounded"
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}