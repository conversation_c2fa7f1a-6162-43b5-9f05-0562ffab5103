import { format, startOfWeek, endOfWeek, eachDayOfInterval, isWeekend, addDays, isSameDay, parseISO } from 'date-fns';
import { User, UserCapacity, Task, TaskEffort, SkillsetGroup } from '../types';

export interface DayCapacity {
  date: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
  isOverCapacity: boolean;
  skillsetBreakdown: {
    skillsetId: string;
    capacity: number;
    demand: number;
    utilizationPercentage: number;
    isOverCapacity: boolean;
  }[];
  tasks: {
    taskId: string;
    title: string;
    demand: number;
    skillsetId: string;
    assignedUserId?: string;
  }[];
}

export interface WeekCapacity {
  weekStart: string;
  weekEnd: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
  isOverCapacity: boolean;
  days: DayCapacity[];
}

export interface UserCapacityData {
  userId: string;
  userName: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
  skillsetCapacities: {
    skillsetId: string;
    skillsetName: string;
    capacity: number;
    demand: number;
    utilizationPercentage: number;
  }[];
}

export function getUserEffectiveCapacity(user: User, userCapacities: UserCapacity[], date: Date): UserCapacity | null {
  const capacity = userCapacities.find(c => {
    if (c.userId !== user.id) return false;
    
    const effectiveFrom = parseISO(c.effectiveFrom);
    const effectiveTo = c.effectiveTo ? parseISO(c.effectiveTo) : null;
    
    return date >= effectiveFrom && (!effectiveTo || date <= effectiveTo);
  });
  
  return capacity || null;
}

export function calculateDayCapacity(
  date: Date,
  users: User[],
  userCapacities: UserCapacity[],
  tasks: Task[],
  taskEfforts: TaskEffort[],
  skillsetGroups: SkillsetGroup[]
): DayCapacity {
  const dateStr = format(date, 'yyyy-MM-dd');
  const dayOfWeek = date.getDay();
  
  // Calculate total capacity for the day
  let totalCapacity = 0;
  const skillsetCapacities = new Map<string, number>();
  
  users.forEach(user => {
    const capacity = getUserEffectiveCapacity(user, userCapacities, date);
    if (!capacity || !capacity.workingDays.includes(dayOfWeek)) return;
    
    totalCapacity += capacity.dailyHours;
    
    // Add skillset-specific capacities
    capacity.skillsetCapacities.forEach(sc => {
      const current = skillsetCapacities.get(sc.skillsetId) || 0;
      skillsetCapacities.set(sc.skillsetId, current + sc.dailyHours);
    });
  });
  
  // Calculate demand from tasks due on this date
  let totalDemand = 0;
  const skillsetDemands = new Map<string, number>();
  const dayTasks: DayCapacity['tasks'] = [];
  
  tasks.forEach(task => {
    if (!task.dueDate) return;
    
    const taskDueDate = parseISO(task.dueDate);
    if (!isSameDay(taskDueDate, date)) return;
    
    const effort = taskEfforts.find(e => e.taskId === task.id);
    if (!effort) return;
    
    effort.skillsetEstimates.forEach(estimate => {
      totalDemand += estimate.estimatedHours;
      
      const current = skillsetDemands.get(estimate.skillsetId) || 0;
      skillsetDemands.set(estimate.skillsetId, current + estimate.estimatedHours);
      
      dayTasks.push({
        taskId: task.id,
        title: task.title,
        demand: estimate.estimatedHours,
        skillsetId: estimate.skillsetId,
        assignedUserId: estimate.assignedUserId,
      });
    });
  });
  
  // Calculate skillset breakdown
  const skillsetBreakdown = skillsetGroups.map(skillset => {
    const capacity = skillsetCapacities.get(skillset.id) || 0;
    const demand = skillsetDemands.get(skillset.id) || 0;
    const utilizationPercentage = capacity > 0 ? (demand / capacity) * 100 : 0;
    
    return {
      skillsetId: skillset.id,
      capacity,
      demand,
      utilizationPercentage,
      isOverCapacity: demand > capacity,
    };
  });
  
  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;
  
  return {
    date: dateStr,
    totalCapacity,
    totalDemand,
    utilizationPercentage,
    isOverCapacity: totalDemand > totalCapacity,
    skillsetBreakdown,
    tasks: dayTasks,
  };
}

export function calculateWeekCapacity(
  weekStart: Date,
  users: User[],
  userCapacities: UserCapacity[],
  tasks: Task[],
  taskEfforts: TaskEffort[],
  skillsetGroups: SkillsetGroup[]
): WeekCapacity {
  const weekEnd = endOfWeek(weekStart);
  const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
  
  const dayCapacities = days.map(day => 
    calculateDayCapacity(day, users, userCapacities, tasks, taskEfforts, skillsetGroups)
  );
  
  const totalCapacity = dayCapacities.reduce((sum, day) => sum + day.totalCapacity, 0);
  const totalDemand = dayCapacities.reduce((sum, day) => sum + day.totalDemand, 0);
  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;
  
  return {
    weekStart: format(weekStart, 'yyyy-MM-dd'),
    weekEnd: format(weekEnd, 'yyyy-MM-dd'),
    totalCapacity,
    totalDemand,
    utilizationPercentage,
    isOverCapacity: totalDemand > totalCapacity,
    days: dayCapacities,
  };
}

export function calculateUserCapacityData(
  user: User,
  userCapacities: UserCapacity[],
  tasks: Task[],
  taskEfforts: TaskEffort[],
  skillsetGroups: SkillsetGroup[],
  dateRange: { start: Date; end: Date }
): UserCapacityData {
  const days = eachDayOfInterval(dateRange);
  
  let totalCapacity = 0;
  let totalDemand = 0;
  const skillsetTotals = new Map<string, { capacity: number; demand: number }>();
  
  days.forEach(date => {
    const capacity = getUserEffectiveCapacity(user, userCapacities, date);
    if (!capacity || !capacity.workingDays.includes(date.getDay())) return;
    
    totalCapacity += capacity.dailyHours;
    
    // Add skillset capacities
    capacity.skillsetCapacities.forEach(sc => {
      const current = skillsetTotals.get(sc.skillsetId) || { capacity: 0, demand: 0 };
      skillsetTotals.set(sc.skillsetId, {
        ...current,
        capacity: current.capacity + sc.dailyHours
      });
    });
    
    // Calculate demand for this user on this date
    tasks.forEach(task => {
      if (!task.dueDate || !isSameDay(parseISO(task.dueDate), date)) return;
      
      const effort = taskEfforts.find(e => e.taskId === task.id);
      if (!effort) return;
      
      effort.skillsetEstimates.forEach(estimate => {
        if (estimate.assignedUserId === user.id) {
          totalDemand += estimate.estimatedHours;
          
          const current = skillsetTotals.get(estimate.skillsetId) || { capacity: 0, demand: 0 };
          skillsetTotals.set(estimate.skillsetId, {
            ...current,
            demand: current.demand + estimate.estimatedHours
          });
        }
      });
    });
  });
  
  const skillsetCapacities = Array.from(skillsetTotals.entries()).map(([skillsetId, totals]) => {
    const skillset = skillsetGroups.find(s => s.id === skillsetId);
    const utilizationPercentage = totals.capacity > 0 ? (totals.demand / totals.capacity) * 100 : 0;
    
    return {
      skillsetId,
      skillsetName: skillset?.name || 'Unknown',
      capacity: totals.capacity,
      demand: totals.demand,
      utilizationPercentage,
    };
  });
  
  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;
  
  return {
    userId: user.id,
    userName: user.name,
    totalCapacity,
    totalDemand,
    utilizationPercentage,
    skillsetCapacities,
  };
}

export function getCapacityStatus(utilizationPercentage: number): {
  status: 'under' | 'optimal' | 'over' | 'critical';
  color: string;
  label: string;
} {
  if (utilizationPercentage <= 70) {
    return { status: 'under', color: 'bg-green-500', label: 'Under Capacity' };
  } else if (utilizationPercentage <= 90) {
    return { status: 'optimal', color: 'bg-blue-500', label: 'Optimal' };
  } else if (utilizationPercentage <= 110) {
    return { status: 'over', color: 'bg-yellow-500', label: 'Over Capacity' };
  } else {
    return { status: 'critical', color: 'bg-red-500', label: 'Critical' };
  }
}
