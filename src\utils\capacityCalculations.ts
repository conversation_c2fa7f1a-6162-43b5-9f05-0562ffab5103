import { format, startOfWeek, endOfWeek, eachDayOfInterval, isWeekend, addDays, isSameDay, parseISO, isWithinInterval } from 'date-fns';
import { User, UserCapacity, Task, TaskEffort, SkillsetGroup } from '../types';

export interface DayCapacity {
  date: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
  isOverCapacity: boolean;
  tasks: {
    taskId: string;
    title: string;
    demand: number;
    assignedUserId?: string;
  }[];
}

export interface WeekCapacity {
  weekStart: string;
  weekEnd: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
  isOverCapacity: boolean;
  days: DayCapacity[];
}

export interface UserCapacityData {
  userId: string;
  userName: string;
  totalCapacity: number;
  totalDemand: number;
  utilizationPercentage: number;
}

export function getWorkingDaysBetween(startDate: Date, endDate: Date, workingDays: number[] = [1, 2, 3, 4, 5]): Date[] {
  const days = eachDayOfInterval({ start: startDate, end: endDate });
  return days.filter(day => workingDays.includes(day.getDay()));
}

export function getUserEffectiveCapacity(user: User, userCapacities: UserCapacity[], date: Date): UserCapacity | null {
  const capacity = userCapacities.find(c => {
    if (c.userId !== user.id) return false;
    
    const effectiveFrom = parseISO(c.effectiveFrom);
    const effectiveTo = c.effectiveTo ? parseISO(c.effectiveTo) : null;
    
    return date >= effectiveFrom && (!effectiveTo || date <= effectiveTo);
  });
  
  return capacity || null;
}

export function calculateDayCapacity(
  date: Date,
  users: User[],
  userCapacities: UserCapacity[],
  tasks: Task[],
  skillsetGroups: SkillsetGroup[]
): DayCapacity {
  const dateStr = format(date, 'yyyy-MM-dd');
  const dayOfWeek = date.getDay();
  
  // Calculate total capacity for the day
  let totalCapacity = 0;

  users.forEach(user => {
    const capacity = getUserEffectiveCapacity(user, userCapacities, date);
    if (!capacity || !capacity.workingDays.includes(dayOfWeek)) return;

    totalCapacity += capacity.dailyHours;
  });
  
  // Calculate demand from tasks that span this date
  let totalDemand = 0;
  const dayTasks: DayCapacity['tasks'] = [];

  // Calculate demand from tasks that span this date

  tasks.forEach(task => {
    if (!task.dueDate || !task.effort) return;

    // Determine task date range
    const taskStartDate = task.startDate ? parseISO(task.startDate) : parseISO(task.dueDate);
    const taskDueDate = parseISO(task.dueDate);

    // Check if the current date falls within the task duration
    const isWithinTaskDuration = isWithinInterval(date, { start: taskStartDate, end: taskDueDate });

    if (!isWithinTaskDuration) return;

    // Calculate working days for this task (use default working days if user capacity not available)
    const workingDays = getWorkingDaysBetween(taskStartDate, taskDueDate, [1, 2, 3, 4, 5]);
    const totalWorkingDays = workingDays.length;

    if (totalWorkingDays === 0) return;

    // Distribute the effort equally across working days
    const dailyDemand = task.effort.estimatedHours / totalWorkingDays;

    totalDemand += dailyDemand;

    dayTasks.push({
      taskId: task.id,
      title: task.title,
      demand: dailyDemand,
      assignedUserId: task.effort.assignedUserId,
    });
  });

  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;

  return {
    date: dateStr,
    totalCapacity,
    totalDemand,
    utilizationPercentage,
    isOverCapacity: totalDemand > totalCapacity,
    tasks: dayTasks,
  };
}

export function calculateWeekCapacity(
  weekStart: Date,
  users: User[],
  userCapacities: UserCapacity[],
  tasks: Task[],
  skillsetGroups: SkillsetGroup[]
): WeekCapacity {
  const weekEnd = endOfWeek(weekStart);
  const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
  
  const dayCapacities = days.map(day =>
    calculateDayCapacity(day, users, userCapacities, tasks, skillsetGroups)
  );
  
  const totalCapacity = dayCapacities.reduce((sum, day) => sum + day.totalCapacity, 0);
  const totalDemand = dayCapacities.reduce((sum, day) => sum + day.totalDemand, 0);
  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;
  
  return {
    weekStart: format(weekStart, 'yyyy-MM-dd'),
    weekEnd: format(weekEnd, 'yyyy-MM-dd'),
    totalCapacity,
    totalDemand,
    utilizationPercentage,
    isOverCapacity: totalDemand > totalCapacity,
    days: dayCapacities,
  };
}

export function calculateUserCapacityData(
  user: User,
  userCapacities: UserCapacity[],
  tasks: Task[],
  skillsetGroups: SkillsetGroup[],
  dateRange: { start: Date; end: Date }
): UserCapacityData {
  const days = eachDayOfInterval(dateRange);
  
  let totalCapacity = 0;
  let totalDemand = 0;

  days.forEach(date => {
    const capacity = getUserEffectiveCapacity(user, userCapacities, date);
    if (!capacity || !capacity.workingDays.includes(date.getDay())) return;

    totalCapacity += capacity.dailyHours;

    // Calculate demand for this user on this date
    tasks.forEach(task => {
      if (!task.dueDate || !task.effort) return;

      // Check if task is assigned to this user
      if (task.effort.assignedUserId !== user.id) return;

      // Determine task date range
      const taskStartDate = task.startDate ? parseISO(task.startDate) : parseISO(task.dueDate);
      const taskDueDate = parseISO(task.dueDate);

      // Check if the current date falls within the task duration
      const isWithinTaskDuration = isWithinInterval(date, { start: taskStartDate, end: taskDueDate });

      if (!isWithinTaskDuration) return;

      // Calculate working days for this task
      const workingDays = getWorkingDaysBetween(taskStartDate, taskDueDate, [1, 2, 3, 4, 5]);
      const totalWorkingDays = workingDays.length;

      if (totalWorkingDays === 0) return;

      // Distribute the effort equally across working days
      const dailyDemand = task.effort.estimatedHours / totalWorkingDays;
      totalDemand += dailyDemand;
    });
  });
  
  const utilizationPercentage = totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0;

  return {
    userId: user.id,
    userName: user.name,
    totalCapacity,
    totalDemand,
    utilizationPercentage,
  };
}

export function getCapacityStatus(utilizationPercentage: number): {
  status: 'under' | 'optimal' | 'over' | 'critical';
  color: string;
  label: string;
} {
  if (utilizationPercentage <= 70) {
    return { status: 'under', color: 'bg-green-500', label: 'Under Capacity' };
  } else if (utilizationPercentage <= 90) {
    return { status: 'optimal', color: 'bg-blue-500', label: 'Optimal' };
  } else if (utilizationPercentage <= 110) {
    return { status: 'over', color: 'bg-yellow-500', label: 'Over Capacity' };
  } else {
    return { status: 'critical', color: 'bg-red-500', label: 'Critical' };
  }
}
