import React, { useState } from 'react';
import { Task, TaskEffort } from '../types';
import { X, ChevronDown, ChevronRight, Folder, FolderOpen } from 'lucide-react';
import { useStore } from '../store/useStore';
import TaskHistory from './TaskHistory';
import TaskComments from './TaskComments';
import TaskDurationStats from './TaskDurationStats';
import SubtaskList from './SubtaskList';
import TaskEffortEstimator from './TaskEffortEstimator';
import FilterDropdown from './FilterDropdown';

interface TaskFormProps {
  onSubmit: (task: Omit<Task, 'id'>) => void;
  onClose: () => void;
  initialData?: Task;
}

export default function TaskForm({ onSubmit, onClose, initialData }: TaskFormProps) {
  // Check if users are being loaded properly
  const { userGroups, projects, users, folders } = useStore();

  // Debug: Log users to console
  console.log('TaskForm users:', users);
  console.log('TaskForm users length:', users.length);
  
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    status: initialData?.status || 'todo' as Task['status'],
    priority: initialData?.priority || 'medium' as Task['priority'],
    startDate: initialData?.startDate || '',
    dueDate: initialData?.dueDate || '',
    tags: initialData?.tags || [] as string[],
    assignedGroups: initialData?.assignedGroups || [] as string[],
    assignedUsers: initialData?.assignedUsers || [] as string[],
    owner: initialData?.owner || '',
    projectId: initialData?.projectId || '',
    folderId: initialData?.folderId || '',
    subtasks: initialData?.subtasks || [],
    comments: initialData?.comments || [],
    history: initialData?.history || [],
    durations: initialData?.durations || [],
    effort: initialData?.effort || undefined,
  });

  const [taskUpdated, setTaskUpdated] = useState(false);
  const [isEffortExpanded, setIsEffortExpanded] = useState(false);

  // Get folder breadcrumb path
  const getFolderPath = (folderId?: string): string => {
    if (!folderId) return '';

    const buildPath = (id: string): string[] => {
      const folder = folders.find(f => f.id === id);
      if (!folder) return [];

      const parentPath = folder.parentId ? buildPath(folder.parentId) : [];
      return [...parentPath, folder.name];
    };

    const path = buildPath(folderId);
    return path.length > 0 ? path.join(' / ') : '';
  };

  // Get project name
  const getProjectName = (projectId?: string): string => {
    if (!projectId) return '';
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : '';
  };

  // Get task location display
  const getTaskLocation = (): string => {
    const projectName = getProjectName(formData.projectId);
    const folderPath = getFolderPath(formData.folderId);

    if (projectName && folderPath) {
      return `${folderPath} / ${projectName}`;
    } else if (projectName) {
      return projectName;
    } else if (folderPath) {
      return folderPath;
    }
    return 'No location';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      comments: initialData?.comments || [],
      history: initialData?.history || [],
      durations: initialData?.durations || []
    });
  };

  const handleSubtaskUpdate = () => {
    setTaskUpdated(!taskUpdated);
  };

  const handleEffortChange = (effort: TaskEffort) => {
    setFormData({ ...formData, effort });
  };

  // Prepare dropdown options
  const userOptions = users.map(user => ({
    value: user.id,
    label: `${user.name} (${user.email})`,
  }));

  const groupOptions = userGroups.map(group => ({
    value: group.id,
    label: group.name,
    color: group.color,
  }));

  // Handle dropdown toggles
  const handleUserToggle = (userId: string) => {
    const newUsers = formData.assignedUsers.includes(userId)
      ? formData.assignedUsers.filter(id => id !== userId)
      : [...formData.assignedUsers, userId];
    setFormData({ ...formData, assignedUsers: newUsers });
  };

  const handleGroupToggle = (groupId: string) => {
    const newGroups = formData.assignedGroups.includes(groupId)
      ? formData.assignedGroups.filter(id => id !== groupId)
      : [...formData.assignedGroups, groupId];
    setFormData({ ...formData, assignedGroups: newGroups });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-8 overflow-auto" onClick={onClose}>
      <div className="bg-white rounded-lg p-6 w-full max-w-[1280px] mb-8" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {initialData ? 'Edit Task' : 'Create New Task'}
          </h2>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-2">
            {initialData && (
              <TaskDurationStats 
                durations={initialData.durations} 
                currentStatus={initialData.status}
              />
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Title and Location Section */}
              <div className="space-y-2">
                <div>
                  <label className="block text-sm font-medium mb-1">Title</label>
                  <input
                    type="text"
                    required
                    className="w-full border rounded-lg p-3 text-lg"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  />
                </div>
                {(formData.projectId || formData.folderId) && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Folder className="w-4 h-4" />
                    <span>Location: {getTaskLocation()}</span>
                  </div>
                )}
              </div>

              {/* Main Properties - Horizontal Layout */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-5 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Project</label>
                    <select
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.projectId}
                      onChange={(e) => setFormData({ ...formData, projectId: e.target.value })}
                    >
                      <option value="">No Project</option>
                      {projects.map((project) => (
                        <option key={project.id} value={project.id}>
                          {project.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Priority</label>
                    <select
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: e.target.value as Task['priority'] })}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Status</label>
                    <select
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as Task['status'] })}
                    >
                      <option value="todo">To Do</option>
                      <option value="in-progress">In Progress</option>
                      <option value="review">Review</option>
                      <option value="done">Done</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Due Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  className="w-full border rounded-lg p-3"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>

              {/* Assignment Section */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <FilterDropdown
                    label="Assigned Users"
                    options={userOptions}
                    selectedValues={formData.assignedUsers}
                    onToggle={handleUserToggle}
                    placeholder="Select users..."
                  />
                </div>

                <div>
                  <FilterDropdown
                    label="Assigned Groups"
                    options={groupOptions}
                    selectedValues={formData.assignedGroups}
                    onToggle={handleGroupToggle}
                    placeholder="Select groups..."
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Owner Group</label>
                <select
                  className="w-full border rounded-lg p-2"
                  value={formData.owner || ''}
                  onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                >
                  <option value="">Select owner group</option>
                  {userGroups.map((group) => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Effort Estimation - Collapsible */}
              <div className="border-t pt-4">
                <button
                  type="button"
                  onClick={() => setIsEffortExpanded(!isEffortExpanded)}
                  className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  {isEffortExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                  <span className="font-medium">Effort Estimation</span>
                  {formData.effort && (
                    <span className="text-sm text-gray-500 ml-auto">
                      {formData.effort.estimatedHours}h estimated
                    </span>
                  )}
                </button>

                {isEffortExpanded && (
                  <div className="mt-4 pl-6">
                    <TaskEffortEstimator
                      taskId={initialData?.id}
                      initialEffort={formData.effort}
                      onEffortChange={handleEffortChange}
                      durations={initialData?.durations || []}
                      currentStatus={formData.status}
                    />
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {initialData ? 'Update Task' : 'Create Task'}
                </button>
              </div>
            </form>

            {initialData && (
              <div className="mt-8">
                <SubtaskList 
                  taskId={initialData.id} 
                  subtasks={initialData.subtasks} 
                  parentTaskStatus={formData.status}
                  onSubtaskUpdate={handleSubtaskUpdate}
                />
              </div>
            )}
          </div>

          {initialData && (
            <div className="space-y-6 h-[calc(100vh-8rem)] overflow-hidden">
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskHistory history={initialData.history} />
              </div>
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskComments taskId={initialData.id} comments={initialData.comments} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}





