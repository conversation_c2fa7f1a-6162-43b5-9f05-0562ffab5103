import React, { useState } from 'react';
import { Task, TaskEffort } from '../types';
import { X } from 'lucide-react';
import { useStore } from '../store/useStore';
import TaskHistory from './TaskHistory';
import TaskComments from './TaskComments';
import TaskDurationStats from './TaskDurationStats';
import SubtaskList from './SubtaskList';
import TaskEffortEstimator from './TaskEffortEstimator';

interface TaskFormProps {
  onSubmit: (task: Omit<Task, 'id'>) => void;
  onClose: () => void;
  initialData?: Task;
}

export default function TaskForm({ onSubmit, onClose, initialData }: TaskFormProps) {
  // Check if users are being loaded properly
  const { userGroups, projects, users } = useStore();
  
  // Debug: Log users to console
  console.log('TaskForm users:', users);
  console.log('TaskForm users length:', users.length);
  
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    status: initialData?.status || 'todo' as Task['status'],
    priority: initialData?.priority || 'medium' as Task['priority'],
    startDate: initialData?.startDate || '',
    dueDate: initialData?.dueDate || '',
    tags: initialData?.tags || [] as string[],
    assignedGroups: initialData?.assignedGroups || [] as string[],
    assignedUsers: initialData?.assignedUsers || [] as string[],
    owner: initialData?.owner || '',
    projectId: initialData?.projectId || '',
    folderId: initialData?.folderId || '',
    subtasks: initialData?.subtasks || [],
    comments: initialData?.comments || [],
    history: initialData?.history || [],
    durations: initialData?.durations || [],
    effort: initialData?.effort || undefined,
  });

  const [taskUpdated, setTaskUpdated] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      comments: initialData?.comments || [],
      history: initialData?.history || [],
      durations: initialData?.durations || []
    });
  };

  const handleSubtaskUpdate = () => {
    setTaskUpdated(!taskUpdated);
  };

  const handleEffortChange = (effort: TaskEffort) => {
    setFormData({ ...formData, effort });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-8 overflow-auto">
      <div className="bg-white rounded-lg p-6 w-full max-w-[1280px] mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {initialData ? 'Edit Task' : 'Create New Task'}
          </h2>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-2">
            {initialData && (
              <TaskDurationStats 
                durations={initialData.durations} 
                currentStatus={initialData.status}
              />
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                  type="text"
                  required
                  className="w-full border rounded-lg p-2"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  className="w-full border rounded-lg p-2"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Project</label>
                <select
                  className="w-full border rounded-lg p-2"
                  value={formData.projectId}
                  onChange={(e) => setFormData({ ...formData, projectId: e.target.value })}
                >
                  <option value="">No Project</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Priority</label>
                  <select
                    className="w-full border rounded-lg p-2"
                    value={formData.priority}
                    onChange={(e) => setFormData({ ...formData, priority: e.target.value as Task['priority'] })}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <select
                    className="w-full border rounded-lg p-2"
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as Task['status'] })}
                  >
                    <option value="todo">To Do</option>
                    <option value="in-progress">In Progress</option>
                    <option value="review">Review</option>
                    <option value="done">Done</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Start Date</label>
                  <input
                    type="date"
                    className="w-full border rounded-lg p-2"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Due Date</label>
                  <input
                    type="date"
                    className="w-full border rounded-lg p-2"
                    value={formData.dueDate}
                    onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Assigned Users</label>
                <select
                  multiple
                  value={formData.assignedUsers}
                  onChange={(e) => {
                    const selectedUsers = Array.from(e.target.selectedOptions, option => option.value);
                    setFormData({ ...formData, assignedUsers: selectedUsers });
                  }}
                  className="w-full border rounded-lg p-2 min-h-[120px]"
                  size={Math.max(5, users.length)}
                >
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </option>
                  ))}
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  Hold Ctrl/Cmd to select multiple users. Selected: {formData.assignedUsers.length}
                </p>
                {users.length === 0 && (
                  <p className="text-sm text-red-500 mt-1">No users available. Add users first.</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Assigned Groups</label>
                <div className="space-y-2">
                  {userGroups.map((group) => (
                    <label key={group.id} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.assignedGroups.includes(group.id)}
                        onChange={(e) => {
                          const newGroups = e.target.checked
                            ? [...formData.assignedGroups, group.id]
                            : formData.assignedGroups.filter(id => id !== group.id);
                          setFormData({ ...formData, assignedGroups: newGroups });
                        }}
                      />
                      <span className="text-sm">{group.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Owner Group</label>
                <select
                  className="w-full border rounded-lg p-2"
                  value={formData.owner || ''}
                  onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                >
                  <option value="">Select owner group</option>
                  {userGroups.map((group) => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Effort Estimation */}
              <div className="border-t pt-4">
                <TaskEffortEstimator
                  taskId={initialData?.id}
                  initialEffort={formData.effort}
                  onEffortChange={handleEffortChange}
                  durations={initialData?.durations || []}
                  currentStatus={formData.status}
                />
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {initialData ? 'Update Task' : 'Create Task'}
                </button>
              </div>
            </form>

            {initialData && (
              <div className="mt-8">
                <SubtaskList 
                  taskId={initialData.id} 
                  subtasks={initialData.subtasks} 
                  parentTaskStatus={formData.status}
                  onSubtaskUpdate={handleSubtaskUpdate}
                />
              </div>
            )}
          </div>

          {initialData && (
            <div className="space-y-6 h-[calc(100vh-8rem)] overflow-hidden">
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskHistory history={initialData.history} />
              </div>
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskComments taskId={initialData.id} comments={initialData.comments} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}





