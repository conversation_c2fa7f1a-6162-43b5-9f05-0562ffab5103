import React, { useState, useMemo } from 'react';
import { format, startOfWeek, endOfWeek, addWeeks, subWeeks, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar, Users, BarChart3, Filter, Download, User, Palette, TrendingUp, AlertTriangle } from 'lucide-react';
import { useStore } from '../store/useStore';
import { calculateDayCapacity, calculateWeekCapacity, calculateUserCapacityData, DayCapacity, UserCapacityData } from '../utils/capacityCalculations';
import CapacityIndicator, { CapacityProgressBar } from './CapacityIndicator';

type ViewMode = 'day' | 'week' | 'month';
type ReportView = 'calendar' | 'skillsets' | 'users' | 'teams' | 'analytics';

interface ResourceFilters {
  selectedUsers: string[];
  selectedSkillsets: string[];
  selectedTeams: string[];
  showOverCapacityOnly: boolean;
  utilizationThreshold: number;
}

export default function ResourceCalendar() {
  const {
    users,
    userCapacities,
    tasks,
    skillsetGroups,
    userGroups
  } = useStore();

  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<ViewMode>('week');
  const [reportView, setReportView] = useState<ReportView>('calendar');
  const [filters, setFilters] = useState<ResourceFilters>({
    selectedUsers: [],
    selectedSkillsets: [],
    selectedTeams: [],
    showOverCapacityOnly: false,
    utilizationThreshold: 80,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Filter users, tasks, and skillsets based on current filters
  const filteredData = useMemo(() => {
    let filteredUsers = users;
    let filteredTasks = tasks;
    let filteredSkillsets = skillsetGroups;

    // Filter by selected users
    if (filters.selectedUsers.length > 0) {
      filteredUsers = users.filter(user => filters.selectedUsers.includes(user.id));
      filteredTasks = tasks.filter(task =>
        task.effort?.assignedUserId && filters.selectedUsers.includes(task.effort.assignedUserId)
      );
    }

    // Filter by selected teams
    if (filters.selectedTeams.length > 0) {
      filteredUsers = filteredUsers.filter(user => filters.selectedTeams.includes(user.groupId));
    }

    // Filter by selected skillsets
    if (filters.selectedSkillsets.length > 0) {
      filteredSkillsets = skillsetGroups.filter(skillset => filters.selectedSkillsets.includes(skillset.id));
      filteredTasks = filteredTasks.filter(task =>
        task.effort?.requiredSkillsets?.some(skillsetId => filters.selectedSkillsets.includes(skillsetId))
      );
    }

    return { filteredUsers, filteredTasks, filteredSkillsets };
  }, [users, tasks, skillsetGroups, filters]);

  // Calculate capacity data based on view mode and filters
  const capacityData = useMemo(() => {
    const { filteredUsers, filteredTasks, filteredSkillsets } = filteredData;

    switch (viewMode) {
      case 'day':
        return calculateDayCapacity(currentDate, filteredUsers, userCapacities, filteredTasks, filteredSkillsets);

      case 'week':
        const weekStart = startOfWeek(currentDate);
        return calculateWeekCapacity(weekStart, filteredUsers, userCapacities, filteredTasks, filteredSkillsets);

      case 'month':
        const monthStart = startOfMonth(currentDate);
        const monthEnd = endOfMonth(currentDate);
        const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
        return monthDays.map(day =>
          calculateDayCapacity(day, filteredUsers, userCapacities, filteredTasks, filteredSkillsets)
        );

      default:
        return null;
    }
  }, [currentDate, viewMode, filteredData, userCapacities]);

  // Calculate report data
  const reportData = useMemo(() => {
    const { filteredUsers, filteredTasks, filteredSkillsets } = filteredData;
    const weekStart = startOfWeek(currentDate);
    const weekEnd = endOfWeek(currentDate);

    // User capacity data
    const userReports = filteredUsers.map(user =>
      calculateUserCapacityData(user, userCapacities, filteredTasks, filteredSkillsets, { start: weekStart, end: weekEnd })
    );

    // Skillset reports - show tasks that require each skillset
    const skillsetReports = filteredSkillsets.map(skillset => {
      const skillsetUsers = filteredUsers.filter(user => user.skillsetIds?.includes(skillset.id));
      const skillsetTasks = filteredTasks.filter(task =>
        task.effort?.requiredSkillsets?.includes(skillset.id)
      );

      const totalCapacity = skillsetUsers.reduce((sum, user) => {
        const capacity = userCapacities.find(c => c.userId === user.id);
        return sum + (capacity?.weeklyHours || 0);
      }, 0);

      const totalDemand = skillsetTasks.reduce((sum, task) => {
        return sum + (task.effort?.estimatedHours || 0);
      }, 0);

      return {
        skillset,
        totalCapacity,
        totalDemand,
        utilizationPercentage: totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0,
        userCount: skillsetUsers.length,
        taskCount: skillsetTasks.length,
      };
    });

    // Team reports
    const teamReports = userGroups.map(team => {
      const teamUsers = filteredUsers.filter(user => user.groupId === team.id);
      const teamUserReports = teamUsers.map(user =>
        calculateUserCapacityData(user, userCapacities, filteredTasks, filteredSkillsets, { start: weekStart, end: weekEnd })
      );

      const totalCapacity = teamUserReports.reduce((sum, report) => sum + report.totalCapacity, 0);
      const totalDemand = teamUserReports.reduce((sum, report) => sum + report.totalDemand, 0);

      return {
        team,
        totalCapacity,
        totalDemand,
        utilizationPercentage: totalCapacity > 0 ? (totalDemand / totalCapacity) * 100 : 0,
        userCount: teamUsers.length,
        users: teamUserReports,
      };
    });

    return { userReports, skillsetReports, teamReports };
  }, [filteredData, currentDate, userCapacities, userGroups]);

  const navigate = (direction: 'prev' | 'next') => {
    switch (viewMode) {
      case 'day':
        setCurrentDate(prev => direction === 'next' ? 
          new Date(prev.getTime() + 24 * 60 * 60 * 1000) : 
          new Date(prev.getTime() - 24 * 60 * 60 * 1000)
        );
        break;
      case 'week':
        setCurrentDate(prev => direction === 'next' ? addWeeks(prev, 1) : subWeeks(prev, 1));
        break;
      case 'month':
        setCurrentDate(prev => {
          const newDate = new Date(prev);
          newDate.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
          return newDate;
        });
        break;
    }
  };

  const getDateRangeLabel = () => {
    switch (viewMode) {
      case 'day':
        return format(currentDate, 'EEEE, MMMM d, yyyy');
      case 'week':
        const weekStart = startOfWeek(currentDate);
        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      default:
        return '';
    }
  };

  const toggleFilter = (filterType: keyof ResourceFilters, value: string) => {
    setFilters(prev => {
      const currentArray = prev[filterType] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];

      return { ...prev, [filterType]: newArray };
    });
  };

  const clearAllFilters = () => {
    setFilters({
      selectedUsers: [],
      selectedSkillsets: [],
      selectedTeams: [],
      showOverCapacityOnly: false,
      utilizationThreshold: 80,
    });
  };

  const renderFilterPanel = () => (
    <div className="bg-gray-800 p-4 rounded-lg mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Filters & Options</h3>
        <button
          onClick={clearAllFilters}
          className="text-sm text-gray-400 hover:text-white"
        >
          Clear All
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Users Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">Users</label>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {users.map(user => (
              <label key={user.id} className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.selectedUsers.includes(user.id)}
                  onChange={() => toggleFilter('selectedUsers', user.id)}
                  className="rounded"
                />
                <span className="text-gray-300">{user.name}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Skillsets Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">Skillsets</label>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {skillsetGroups.map(skillset => (
              <label key={skillset.id} className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.selectedSkillsets.includes(skillset.id)}
                  onChange={() => toggleFilter('selectedSkillsets', skillset.id)}
                  className="rounded"
                />
                <div className="flex items-center gap-1">
                  <div className={`w-3 h-3 rounded-full ${skillset.color}`} />
                  <span className="text-gray-300">{skillset.name}</span>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Teams Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">Teams</label>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {userGroups.map(team => (
              <label key={team.id} className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={filters.selectedTeams.includes(team.id)}
                  onChange={() => toggleFilter('selectedTeams', team.id)}
                  className="rounded"
                />
                <span className="text-gray-300">{team.name}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Options */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">Options</label>
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={filters.showOverCapacityOnly}
                onChange={(e) => setFilters(prev => ({ ...prev, showOverCapacityOnly: e.target.checked }))}
                className="rounded"
              />
              <span className="text-gray-300">Over-capacity only</span>
            </label>

            <div>
              <label className="block text-xs text-gray-400 mb-1">
                Utilization Threshold: {filters.utilizationThreshold}%
              </label>
              <input
                type="range"
                min="50"
                max="150"
                value={filters.utilizationThreshold}
                onChange={(e) => setFilters(prev => ({ ...prev, utilizationThreshold: parseInt(e.target.value) }))}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSkillsetReport = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-900">
          <Palette className="w-5 h-5" />
          Skillset Capacity Report
        </h3>

        <div className="grid gap-4">
          {reportData.skillsetReports.map((report) => (
            <div key={report.skillset.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className={`w-4 h-4 rounded-full ${report.skillset.color}`} />
                  <h4 className="font-medium text-gray-900">{report.skillset.name}</h4>
                  <span className="text-sm text-gray-500">
                    ({report.userCount} users, {report.taskCount} tasks)
                  </span>
                </div>
                <CapacityIndicator
                  utilizationPercentage={report.utilizationPercentage}
                  capacity={report.totalCapacity}
                  demand={report.totalDemand}
                  size="sm"
                />
              </div>

              <CapacityProgressBar
                utilizationPercentage={report.utilizationPercentage}
                capacity={report.totalCapacity}
                demand={report.totalDemand}
                showLabels={true}
                height="h-3"
              />

              <div className="grid grid-cols-3 gap-4 mt-3 text-sm">
                <div>
                  <span className="text-gray-600">Capacity:</span>
                  <div className="font-medium">{report.totalCapacity.toFixed(1)}h</div>
                </div>
                <div>
                  <span className="text-gray-600">Demand:</span>
                  <div className="font-medium">{report.totalDemand.toFixed(1)}h</div>
                </div>
                <div>
                  <span className="text-gray-600">Utilization:</span>
                  <div className="font-medium">{report.utilizationPercentage.toFixed(0)}%</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderUserReport = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-900">
          <User className="w-5 h-5" />
          User Capacity Report
        </h3>

        <div className="grid gap-4">
          {reportData.userReports.map((report) => {
            const user = users.find(u => u.id === report.userId);
            const userGroup = userGroups.find(g => g.id === user?.groupId);

            return (
              <div key={report.userId} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{report.userName}</h4>
                    {userGroup && (
                      <span className="text-sm text-gray-500">{userGroup.name}</span>
                    )}
                  </div>
                  <CapacityIndicator
                    utilizationPercentage={report.utilizationPercentage}
                    capacity={report.totalCapacity}
                    demand={report.totalDemand}
                    size="sm"
                  />
                </div>

                <CapacityProgressBar
                  utilizationPercentage={report.utilizationPercentage}
                  capacity={report.totalCapacity}
                  demand={report.totalDemand}
                  showLabels={true}
                  height="h-3"
                />

                {/* User skillsets display */}
                {user?.skillsetIds && user.skillsetIds.length > 0 && (
                  <div className="mt-3">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Skillsets</h5>
                    <div className="flex flex-wrap gap-1">
                      {user.skillsetIds.map((skillsetId) => {
                        const skillset = skillsetGroups.find(s => s.id === skillsetId);
                        return skillset ? (
                          <div key={skillsetId} className="flex items-center gap-1 text-xs bg-gray-50 px-2 py-1 rounded">
                            <div className={`w-2 h-2 rounded-full ${skillset.color}`} />
                            <span className="text-gray-900">{skillset.name}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  const renderTeamReport = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-900">
          <Users className="w-5 h-5" />
          Team Capacity Report
        </h3>

        <div className="grid gap-6">
          {reportData.teamReports.map((report) => (
            <div key={report.team.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-lg text-gray-900">{report.team.name}</h4>
                  <span className="text-sm text-gray-500">
                    {report.userCount} team members
                  </span>
                </div>
                <CapacityIndicator
                  utilizationPercentage={report.utilizationPercentage}
                  capacity={report.totalCapacity}
                  demand={report.totalDemand}
                  size="md"
                />
              </div>

              <CapacityProgressBar
                utilizationPercentage={report.utilizationPercentage}
                capacity={report.totalCapacity}
                demand={report.totalDemand}
                showLabels={true}
                height="h-4"
              />

              {/* Team members breakdown */}
              <div className="mt-4">
                <h5 className="text-sm font-medium text-gray-700 mb-3">Team Members</h5>
                <div className="grid gap-2">
                  {report.users.map((userReport) => (
                    <div key={userReport.userId} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm text-gray-900">{userReport.userName}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-600">
                          {userReport.totalDemand.toFixed(1)}h / {userReport.totalCapacity.toFixed(1)}h
                        </span>
                        <CapacityIndicator
                          utilizationPercentage={userReport.utilizationPercentage}
                          capacity={userReport.totalCapacity}
                          demand={userReport.totalDemand}
                          size="sm"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderAnalyticsReport = () => {
    const overCapacityUsers = reportData.userReports.filter(r => r.utilizationPercentage > 100);
    const underUtilizedUsers = reportData.userReports.filter(r => r.utilizationPercentage < 70);
    const criticalSkillsets = reportData.skillsetReports.filter(r => r.utilizationPercentage > 110);

    return (
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span className="font-medium">Over Capacity</span>
            </div>
            <div className="text-2xl font-bold text-red-600">{overCapacityUsers.length}</div>
            <div className="text-sm text-gray-600">users</div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-5 h-5 text-yellow-500" />
              <span className="font-medium">Under Utilized</span>
            </div>
            <div className="text-2xl font-bold text-yellow-600">{underUtilizedUsers.length}</div>
            <div className="text-sm text-gray-600">users</div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center gap-2 mb-2">
              <Palette className="w-5 h-5 text-red-500" />
              <span className="font-medium">Critical Skillsets</span>
            </div>
            <div className="text-2xl font-bold text-red-600">{criticalSkillsets.length}</div>
            <div className="text-sm text-gray-600">skillsets</div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-5 h-5 text-blue-500" />
              <span className="font-medium">Avg Utilization</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {(reportData.userReports.reduce((sum, r) => sum + r.utilizationPercentage, 0) / reportData.userReports.length || 0).toFixed(0)}%
            </div>
            <div className="text-sm text-gray-600">across all users</div>
          </div>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Over Capacity Users */}
          {overCapacityUsers.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="font-medium text-red-600 mb-4 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Over Capacity Users
              </h4>
              <div className="space-y-2">
                {overCapacityUsers.map(user => (
                  <div key={user.userId} className="flex items-center justify-between p-2 bg-red-50 rounded">
                    <span className="text-sm text-gray-900">{user.userName}</span>
                    <span className="text-sm font-medium text-red-600">
                      {user.utilizationPercentage.toFixed(0)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Critical Skillsets */}
          {criticalSkillsets.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="font-medium text-red-600 mb-4 flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Critical Skillsets
              </h4>
              <div className="space-y-2">
                {criticalSkillsets.map(skillset => (
                  <div key={skillset.skillset.id} className="flex items-center justify-between p-2 bg-red-50 rounded">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${skillset.skillset.color}`} />
                      <span className="text-sm text-gray-900">{skillset.skillset.name}</span>
                    </div>
                    <span className="text-sm font-medium text-red-600">
                      {skillset.utilizationPercentage.toFixed(0)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderDayView = (dayData: DayCapacity) => (
    <div className="space-y-6">
      {/* Day Summary */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">Capacity Overview</h3>
        <CapacityIndicator
          utilizationPercentage={dayData.utilizationPercentage}
          capacity={dayData.totalCapacity}
          demand={dayData.totalDemand}
          size="lg"
          showDetails={true}
        />
      </div>

      {/* Skillset Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">Skillset Breakdown</h3>
        <div className="space-y-4">
          {dayData.skillsetBreakdown.map((skillset) => {
            const skillsetInfo = skillsetGroups.find(s => s.id === skillset.skillsetId);
            if (!skillsetInfo || skillset.capacity === 0) return null;
            
            return (
              <div key={skillset.skillsetId} className="border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${skillsetInfo.color}`} />
                  <span className="font-medium text-gray-900">{skillsetInfo.name}</span>
                </div>
                <CapacityProgressBar
                  utilizationPercentage={skillset.utilizationPercentage}
                  capacity={skillset.capacity}
                  demand={skillset.demand}
                  showLabels={true}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Tasks */}
      {dayData.tasks.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">Tasks Due Today</h3>
          <div className="space-y-3">
            {dayData.tasks.map((task, index) => {
              const skillsetInfo = skillsetGroups.find(s => s.id === task.skillsetId);
              const assignedUser = users.find(u => u.id === task.assignedUserId);
              
              return (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className={`w-2 h-2 rounded-full ${skillsetInfo?.color || 'bg-gray-400'}`} />
                        <span className="text-gray-900">{skillsetInfo?.name || 'Unknown'}</span>
                        {assignedUser && (
                          <>
                            <span>•</span>
                            <span className="text-gray-900">{assignedUser.name}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{task.demand}h</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );

  const renderWeekView = (weekData: any) => (
    <div className="space-y-6">
      {/* Week Summary */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">Week Overview</h3>
        <CapacityIndicator
          utilizationPercentage={weekData.utilizationPercentage}
          capacity={weekData.totalCapacity}
          demand={weekData.totalDemand}
          size="lg"
          showDetails={true}
        />
      </div>

      {/* Daily Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Daily Breakdown</h3>
        <div className="grid grid-cols-7 gap-4">
          {weekData.days.map((day: DayCapacity) => (
            <div key={day.date} className="text-center">
              <div className="text-sm font-medium mb-2">
                {format(new Date(day.date), 'EEE')}
              </div>
              <div className="text-xs text-gray-600 mb-2">
                {format(new Date(day.date), 'MMM d')}
              </div>
              <CapacityIndicator
                utilizationPercentage={day.utilizationPercentage}
                capacity={day.totalCapacity}
                demand={day.totalDemand}
                size="sm"
              />
              <div className="text-xs text-gray-600 mt-1">
                {day.totalDemand.toFixed(1)}h / {day.totalCapacity.toFixed(1)}h
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMonthView = (monthData: DayCapacity[]) => (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Month Overview</h3>
      <div className="grid grid-cols-7 gap-2">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-sm font-medium text-gray-600 p-2">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {monthData.map((day) => {
          const date = new Date(day.date);
          const isCurrentMonth = isSameMonth(date, currentDate);
          const isCurrentDay = isToday(date);
          
          return (
            <div
              key={day.date}
              className={`p-2 border rounded ${
                isCurrentMonth ? 'bg-white' : 'bg-gray-50'
              } ${isCurrentDay ? 'ring-2 ring-blue-500' : ''}`}
            >
              <div className="text-sm font-medium mb-1">
                {format(date, 'd')}
              </div>
              {day.totalCapacity > 0 && (
                <CapacityIndicator
                  utilizationPercentage={day.utilizationPercentage}
                  capacity={day.totalCapacity}
                  demand={day.totalDemand}
                  size="sm"
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Calendar className="w-6 h-6" />
              Resource Calendar
            </h1>
            <p className="text-gray-400">Capacity planning and resource management</p>
          </div>

          <div className="flex items-center gap-4">
            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-3 py-2 rounded-lg flex items-center gap-2 ${
                showFilters ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
              }`}
            >
              <Filter className="w-4 h-4" />
              Filters
            </button>

            {/* Export Button */}
            <button className="px-3 py-2 bg-gray-800 text-gray-400 hover:text-white rounded-lg flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Report View Tabs */}
        <div className="flex bg-gray-800 rounded-lg p-1 mb-6">
          {([
            { id: 'calendar', label: 'Calendar', icon: Calendar },
            { id: 'skillsets', label: 'Skillsets', icon: Palette },
            { id: 'users', label: 'Users', icon: User },
            { id: 'teams', label: 'Teams', icon: Users },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ] as Array<{ id: ReportView; label: string; icon: any }>).map((tab) => (
            <button
              key={tab.id}
              onClick={() => setReportView(tab.id)}
              className={`px-4 py-2 rounded text-sm flex items-center gap-2 ${
                reportView === tab.id ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Calendar View Controls */}
        {reportView === 'calendar' && (
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              {/* View Mode Selector */}
              <div className="flex bg-gray-800 rounded-lg p-1">
                {(['day', 'week', 'month'] as ViewMode[]).map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode)}
                    className={`px-3 py-1 rounded text-sm capitalize ${
                      viewMode === mode ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {mode}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        {showFilters && renderFilterPanel()}

        {/* Navigation (only for calendar view) */}
        {reportView === 'calendar' && (
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('prev')}
                className="p-2 hover:bg-gray-800 rounded"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <h2 className="text-xl font-semibold">{getDateRangeLabel()}</h2>
              <button
                onClick={() => navigate('next')}
                className="p-2 hover:bg-gray-800 rounded"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>

            <button
              onClick={() => setCurrentDate(new Date())}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Today
            </button>
          </div>
        )}

        {/* Content */}
        <div>
          {reportView === 'calendar' && (
            <>
              {viewMode === 'day' && capacityData && renderDayView(capacityData as DayCapacity)}
              {viewMode === 'week' && capacityData && renderWeekView(capacityData)}
              {viewMode === 'month' && capacityData && renderMonthView(capacityData as DayCapacity[])}
            </>
          )}
          {reportView === 'skillsets' && renderSkillsetReport()}
          {reportView === 'users' && renderUserReport()}
          {reportView === 'teams' && renderTeamReport()}
          {reportView === 'analytics' && renderAnalyticsReport()}
        </div>
      </div>
    </div>
  );
}
