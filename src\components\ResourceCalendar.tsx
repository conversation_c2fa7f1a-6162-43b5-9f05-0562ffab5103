import React, { useState, useMemo } from 'react';
import { format, startOfWeek, addWeeks, subWeeks, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar, Users, BarChart3, Filter } from 'lucide-react';
import { useStore } from '../store/useStore';
import { calculateDayCapacity, calculateWeekCapacity, DayCapacity } from '../utils/capacityCalculations';
import CapacityIndicator, { CapacityProgressBar } from './CapacityIndicator';

type ViewMode = 'day' | 'week' | 'month';

export default function ResourceCalendar() {
  const {
    users,
    userCapacities,
    tasks,
    skillsetGroups
  } = useStore();
  
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<ViewMode>('week');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showOverCapacityOnly, setShowOverCapacityOnly] = useState(false);

  // Calculate capacity data based on view mode
  const capacityData = useMemo(() => {
    switch (viewMode) {
      case 'day':
        return calculateDayCapacity(currentDate, users, userCapacities, tasks, skillsetGroups);

      case 'week':
        const weekStart = startOfWeek(currentDate);
        return calculateWeekCapacity(weekStart, users, userCapacities, tasks, skillsetGroups);

      case 'month':
        const monthStart = startOfMonth(currentDate);
        const monthEnd = endOfMonth(currentDate);
        const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
        return monthDays.map(day =>
          calculateDayCapacity(day, users, userCapacities, tasks, skillsetGroups)
        );

      default:
        return null;
    }
  }, [currentDate, viewMode, users, userCapacities, tasks, skillsetGroups]);

  const navigate = (direction: 'prev' | 'next') => {
    switch (viewMode) {
      case 'day':
        setCurrentDate(prev => direction === 'next' ? 
          new Date(prev.getTime() + 24 * 60 * 60 * 1000) : 
          new Date(prev.getTime() - 24 * 60 * 60 * 1000)
        );
        break;
      case 'week':
        setCurrentDate(prev => direction === 'next' ? addWeeks(prev, 1) : subWeeks(prev, 1));
        break;
      case 'month':
        setCurrentDate(prev => {
          const newDate = new Date(prev);
          newDate.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
          return newDate;
        });
        break;
    }
  };

  const getDateRangeLabel = () => {
    switch (viewMode) {
      case 'day':
        return format(currentDate, 'EEEE, MMMM d, yyyy');
      case 'week':
        const weekStart = startOfWeek(currentDate);
        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      default:
        return '';
    }
  };

  const renderDayView = (dayData: DayCapacity) => (
    <div className="space-y-6">
      {/* Day Summary */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Capacity Overview</h3>
        <CapacityIndicator
          utilizationPercentage={dayData.utilizationPercentage}
          capacity={dayData.totalCapacity}
          demand={dayData.totalDemand}
          size="lg"
          showDetails={true}
        />
      </div>

      {/* Skillset Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Skillset Breakdown</h3>
        <div className="space-y-4">
          {dayData.skillsetBreakdown.map((skillset) => {
            const skillsetInfo = skillsetGroups.find(s => s.id === skillset.skillsetId);
            if (!skillsetInfo || skillset.capacity === 0) return null;
            
            return (
              <div key={skillset.skillsetId} className="border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${skillsetInfo.color}`} />
                  <span className="font-medium">{skillsetInfo.name}</span>
                </div>
                <CapacityProgressBar
                  utilizationPercentage={skillset.utilizationPercentage}
                  capacity={skillset.capacity}
                  demand={skillset.demand}
                  showLabels={true}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* Tasks */}
      {dayData.tasks.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">Tasks Due Today</h3>
          <div className="space-y-3">
            {dayData.tasks.map((task, index) => {
              const skillsetInfo = skillsetGroups.find(s => s.id === task.skillsetId);
              const assignedUser = users.find(u => u.id === task.assignedUserId);
              
              return (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{task.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className={`w-2 h-2 rounded-full ${skillsetInfo?.color || 'bg-gray-400'}`} />
                        <span>{skillsetInfo?.name || 'Unknown'}</span>
                        {assignedUser && (
                          <>
                            <span>•</span>
                            <span>{assignedUser.name}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{task.demand}h</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );

  const renderWeekView = (weekData: any) => (
    <div className="space-y-6">
      {/* Week Summary */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Week Overview</h3>
        <CapacityIndicator
          utilizationPercentage={weekData.utilizationPercentage}
          capacity={weekData.totalCapacity}
          demand={weekData.totalDemand}
          size="lg"
          showDetails={true}
        />
      </div>

      {/* Daily Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Daily Breakdown</h3>
        <div className="grid grid-cols-7 gap-4">
          {weekData.days.map((day: DayCapacity) => (
            <div key={day.date} className="text-center">
              <div className="text-sm font-medium mb-2">
                {format(new Date(day.date), 'EEE')}
              </div>
              <div className="text-xs text-gray-600 mb-2">
                {format(new Date(day.date), 'MMM d')}
              </div>
              <CapacityIndicator
                utilizationPercentage={day.utilizationPercentage}
                capacity={day.totalCapacity}
                demand={day.totalDemand}
                size="sm"
              />
              <div className="text-xs text-gray-600 mt-1">
                {day.totalDemand.toFixed(1)}h / {day.totalCapacity.toFixed(1)}h
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderMonthView = (monthData: DayCapacity[]) => (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Month Overview</h3>
      <div className="grid grid-cols-7 gap-2">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-sm font-medium text-gray-600 p-2">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {monthData.map((day) => {
          const date = new Date(day.date);
          const isCurrentMonth = isSameMonth(date, currentDate);
          const isCurrentDay = isToday(date);
          
          return (
            <div
              key={day.date}
              className={`p-2 border rounded ${
                isCurrentMonth ? 'bg-white' : 'bg-gray-50'
              } ${isCurrentDay ? 'ring-2 ring-blue-500' : ''}`}
            >
              <div className="text-sm font-medium mb-1">
                {format(date, 'd')}
              </div>
              {day.totalCapacity > 0 && (
                <CapacityIndicator
                  utilizationPercentage={day.utilizationPercentage}
                  capacity={day.totalCapacity}
                  demand={day.totalDemand}
                  size="sm"
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Calendar className="w-6 h-6" />
              Resource Calendar
            </h1>
            <p className="text-gray-400">Capacity planning and resource management</p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* View Mode Selector */}
            <div className="flex bg-gray-800 rounded-lg p-1">
              {(['day', 'week', 'month'] as ViewMode[]).map((mode) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode)}
                  className={`px-3 py-1 rounded text-sm capitalize ${
                    viewMode === mode ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {mode}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('prev')}
              className="p-2 hover:bg-gray-800 rounded"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-semibold">{getDateRangeLabel()}</h2>
            <button
              onClick={() => navigate('next')}
              className="p-2 hover:bg-gray-800 rounded"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
          
          <button
            onClick={() => setCurrentDate(new Date())}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Today
          </button>
        </div>

        {/* Content */}
        <div>
          {viewMode === 'day' && capacityData && renderDayView(capacityData as DayCapacity)}
          {viewMode === 'week' && capacityData && renderWeekView(capacityData)}
          {viewMode === 'month' && capacityData && renderMonthView(capacityData as DayCapacity[])}
        </div>
      </div>
    </div>
  );
}
