export interface Comment {
  id: string;
  content: string;
  userId: string;
  timestamp: string | Date;
  edited?: boolean;
  replies?: Comment[];
}
export interface TaskComment {
  id: string;
  taskId: string;
  userId: string;
  content: string;
  timestamp: string;
  edited?: boolean;
  parentId?: string;
  replies?: TaskComment[];
}

export interface TaskHistoryEntry {
  id: string;
  taskId: string;
  timestamp: string;
  field: string;
  oldValue: string;
  newValue: string;
  userId: string;
}

export interface TaskDuration {
  status: string;
  startTime: string;
  endTime?: string;
}

export interface Subtask {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  assignedUserId?: string;
  priority?: 'low' | 'medium' | 'high';
  startDate?: string;
  dueDate?: string;
  assignedGroups?: string[];
  owner?: string;
  status: 'todo' | 'in-progress' | 'review' | 'done';
  comments: TaskComment[];
  history: TaskHistoryEntry[];
  durations: TaskDuration[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  groupId: string;
  avatar?: string;
  skillsetIds: string[];
  capacity?: UserCapacity;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high';
  assignedUserId?: string;
  dueDate?: string;
  startDate?: string;
  tags: string[];
  projectId?: string;
  assignedGroups?: string[];
  assignedUsers?: string[];
  owner?: string;
  ownerId?: string;
  folderId?: string;
  comments: TaskComment[];
  history: TaskHistoryEntry[];
  durations: TaskDuration[];
  subtasks: Subtask[];
  effort?: TaskEffort;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  color: string;
  tasks: string[];
  startDate?: string;
  endDate?: string;
  folderId?: string;
  effort?: {
    skillsetEstimates: {
      skillsetId: string;
      estimatedHours: number;
    }[];
    totalEstimatedHours: number;
  };
}

export interface UserGroup {
  id: string;
  name: string;
  color: string;
}

export interface KanbanColumn {
  id: string;
  title: string;
  color: string;
}

export interface Folder {
  id: string;
  name: string;
  parentId?: string;
}

export interface TaskFilter {
  assignedUsers: string[];
  status: string[];
  priority: string[];
  owners: string[];
  assignedGroups: string[];
  tags: string[];
  dueDateRange: {
    start?: string;
    end?: string;
  };
  hasOverdueTasks?: boolean;
}

export interface SkillsetGroup {
  id: string;
  name: string;
  description?: string;
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserCapacity {
  userId: string;
  dailyHours: number;
  weeklyHours: number;
  workingDays: number[]; // 0-6 (Sunday-Saturday)
  skillsetCapacities: {
    skillsetId: string;
    dailyHours: number;
    weeklyHours: number;
  }[];
  effectiveFrom: string;
  effectiveTo?: string;
}

export interface TaskEffort {
  taskId: string;
  skillsetEstimates: {
    skillsetId: string;
    estimatedHours: number;
    actualHours?: number;
    assignedUserId?: string;
  }[];
  totalEstimatedHours: number;
  totalActualHours?: number;
}