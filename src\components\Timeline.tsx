// eslint-disable-next-line @typescript-eslint/no-unused-vars
import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { format, addDays, differenceInDays, parseISO, isValid } from 'date-fns';
import { Calendar, Filter } from 'lucide-react';

export default function Timeline() {
  const { tasks, projects, userGroups } = useStore();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');
  const today = new Date();
  
  const filteredTasks = tasks.filter(task => {
    const projectMatch = selectedProject === 'all' || task.projectId === selectedProject;
    const groupMatch = selectedGroup === 'all' || 
      (task.assignedGroups && task.assignedGroups.includes(selectedGroup));
    return projectMatch && groupMatch;
  });

  const dates = filteredTasks
    .map(task => ({
      start: task.startDate ? parseISO(task.startDate) : today,
      end: task.dueDate ? parseISO(task.dueDate) : addDays(today, 7)
    }))
    .filter(({ start, end }) => isValid(start) && isValid(end));

  const earliestDate = dates.length > 0 
    ? dates.reduce((min, curr) => curr.start < min ? curr.start : min, dates[0].start)
    : today;
    
  const latestDate = dates.length > 0
    ? dates.reduce((max, curr) => curr.end > max ? curr.end : max, dates[0].end)
    : addDays(today, 30);

  const totalDays = differenceInDays(latestDate, earliestDate) + 1;
  const daysArray = Array.from({ length: totalDays }, (_, i) => addDays(earliestDate, i));

  return (
    <div className="p-8">
      <div className="max-w-[1600px] mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <Calendar className="w-6 h-6 text-blue-600" />
            <h2 className="text-2xl font-bold">Timeline</h2>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="border rounded-lg px-3 py-2"
              >
                <option value="all">All Projects</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>

              <select
                value={selectedGroup}
                onChange={(e) => setSelectedGroup(e.target.value)}
                className="border rounded-lg px-3 py-2"
              >
                <option value="all">All Teams</option>
                {userGroups.map((group) => (
                  <option key={group.id} value={group.id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm">
          <div className="overflow-x-auto">
            <div className="min-w-[800px]">
              <div className="sticky top-0 bg-white z-10 border-b">
                <div className="flex">
                  <div className="w-1/4 p-4 font-semibold">Task</div>
                  <div className="flex-1 flex">
                    {daysArray.map(date => (
                      <div
                        key={date.toISOString()}
                        className={`flex-1 p-2 text-center text-sm ${
                          format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')
                            ? 'bg-blue-50'
                            : ''
                        }`}
                      >
                        <div className="font-medium">{format(date, 'MMM d')}</div>
                        <div className="text-xs text-gray-500">{format(date, 'EEE')}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="relative">
                {projects.map(project => {
                  const projectTasks = filteredTasks.filter(task => task.projectId === project.id);
                  if (projectTasks.length === 0) return null;

                  return (
                    <div key={project.id} className="mb-8">
                      <div className="sticky left-0 flex items-center gap-2 px-4 py-2 bg-gray-50 z-10">
                        <div className={`w-3 h-3 rounded-full ${project.color}`} />
                        <h3 className="font-semibold">{project.name}</h3>
                      </div>

                      {projectTasks.map(task => {
                        const startDate = task.startDate ? parseISO(task.startDate) : today;
                        const endDate = task.dueDate ? parseISO(task.dueDate) : addDays(today, 7);
                        
                        if (!isValid(startDate) || !isValid(endDate)) return null;

                        const startOffset = differenceInDays(startDate, earliestDate);
                        const duration = differenceInDays(endDate, startDate) + 1;
                        
                        return (
                          <div key={task.id} className="flex items-center hover:bg-gray-50">
                            <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                              <h4 className="font-medium">{task.title}</h4>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {task.assignedGroups?.map(groupId => {
                                  const group = userGroups.find(g => g.id === groupId);
                                  return group ? (
                                    <span
                                      key={groupId}
                                      className={`px-2 py-0.5 rounded-full text-xs ${group.color} bg-opacity-20`}
                                    >
                                      {group.name}
                                    </span>
                                  ) : null;
                                })}
                              </div>
                            </div>
                            <div className="flex-1 flex items-center min-h-[4rem] px-[1px]">
                              <div
                                className={`h-8 rounded-lg ${
                                  task.status === 'done'
                                    ? 'bg-green-200'
                                    : task.status === 'in-progress'
                                    ? 'bg-blue-200'
                                    : 'bg-gray-200'
                                }`}
                                style={{
                                  marginLeft: `${(startOffset / totalDays) * 100}%`,
                                  width: `${(duration / totalDays) * 100}%`
                                }}
                              >
                                <div className="px-2 py-1 text-sm truncate">
                                  {task.title}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}

                {filteredTasks.filter(task => !task.projectId).length > 0 && (
                  <div className="mb-8">
                    <div className="sticky left-0 flex items-center gap-2 px-4 py-2 bg-gray-50 z-10">
                      <h3 className="font-semibold">Other Tasks</h3>
                    </div>

                    {filteredTasks
                      .filter(task => !task.projectId)
                      .map(task => {
                        const startDate = task.startDate ? parseISO(task.startDate) : today;
                        const endDate = task.dueDate ? parseISO(task.dueDate) : addDays(today, 7);
                        
                        if (!isValid(startDate) || !isValid(endDate)) return null;

                        const startOffset = differenceInDays(startDate, earliestDate);
                        const duration = differenceInDays(endDate, startDate) + 1;
                        
                        return (
                          <div key={task.id} className="flex items-center hover:bg-gray-50">
                            <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                              <h4 className="font-medium">{task.title}</h4>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {task.assignedGroups?.map(groupId => {
                                  const group = userGroups.find(g => g.id === groupId);
                                  return group ? (
                                    <span
                                      key={groupId}
                                      className={`px-2 py-0.5 rounded-full text-xs ${group.color} bg-opacity-20`}
                                    >
                                      {group.name}
                                    </span>
                                  ) : null;
                                })}
                              </div>
                            </div>
                            <div className="flex-1 flex items-center min-h-[4rem] px-[1px]">
                              <div
                                className={`h-8 rounded-lg ${
                                  task.status === 'done'
                                    ? 'bg-green-200'
                                    : task.status === 'in-progress'
                                    ? 'bg-blue-200'
                                    : 'bg-gray-200'
                                }`}
                                style={{
                                  marginLeft: `${(startOffset / totalDays) * 100}%`,
                                  width: `${(duration / totalDays) * 100}%`
                                }}
                              >
                                <div className="px-2 py-1 text-sm truncate">
                                  {task.title}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}